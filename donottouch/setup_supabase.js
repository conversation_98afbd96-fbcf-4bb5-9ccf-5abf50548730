const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Check if .env file exists
if (!fs.existsSync(path.join(__dirname, '.env'))) {
  console.error('Error: .env file not found. Please create a .env file with SUPABASE_URL and SUPABASE_KEY.');
  process.exit(1);
}

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Error: SUPABASE_URL and SUPABASE_KEY must be set in the .env file.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Read SQL file
const sqlFile = path.join(__dirname, 'database_setup.sql');
const sql = fs.readFileSync(sqlFile, 'utf8');

// Split SQL into individual statements
const statements = sql.split(';').filter(statement => statement.trim() !== '');

async function setupDatabase() {
  console.log('Setting up database...');
  
  try {
    // Execute each SQL statement
    for (const statement of statements) {
      const { error } = await supabase.rpc('exec_sql', { sql: statement });
      
      if (error) {
        console.error('Error executing SQL:', error);
        console.error('Statement:', statement);
      }
    }
    
    console.log('Database setup complete!');
  } catch (error) {
    console.error('Error setting up database:', error);
  }
}

// Create initial creatures
async function createInitialCreatures() {
  console.log('Creating initial creatures...');
  
  const creatures = [
    {
      name: 'Fluffpaw',
      type: 'normal',
      level: 1,
      hp: 10,
      max_hp: 10,
      attack: 5,
      defense: 5,
      speed: 5
    },
    {
      name: 'Emberling',
      type: 'fire',
      level: 1,
      hp: 8,
      max_hp: 8,
      attack: 7,
      defense: 4,
      speed: 6
    },
    {
      name: 'Bubblefin',
      type: 'water',
      level: 1,
      hp: 12,
      max_hp: 12,
      attack: 4,
      defense: 6,
      speed: 4
    }
  ];
  
  for (const creature of creatures) {
    const { data, error } = await supabase
      .from('creatures')
      .insert([creature]);
      
    if (error) {
      console.error('Error creating creature:', error);
    }
  }
  
  console.log('Initial creatures created!');
}

// Main function
async function main() {
  await setupDatabase();
  await createInitialCreatures();
  console.log('Setup complete!');
}

main().catch(console.error);
