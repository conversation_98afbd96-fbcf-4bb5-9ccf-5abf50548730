# CreatureQuest Deployment Guide

This guide will help you deploy the CreatureQuest game to a production environment.

## Prerequisites

- Node.js (v14 or higher)
- npm or yarn
- A Supabase account
- A hosting platform (Heroku, Vercel, Netlify, etc.)

## Step 1: Set Up Supabase

1. Create a new Supabase project at [https://app.supabase.io/](https://app.supabase.io/)
2. Once your project is created, go to the SQL Editor
3. Copy and paste the contents of `database_setup.sql` into the SQL Editor and run it
4. Go to Settings > API to get your Supabase URL and anon key

## Step 2: Configure Environment Variables

1. Create a `.env` file in the root directory (or set environment variables on your hosting platform)
2. Add the following variables:
   ```
   SUPABASE_URL=your-supabase-url
   SUPABASE_KEY=your-supabase-anon-key
   PORT=3000
   ```

## Step 3: Install Dependencies

```bash
npm install
```

## Step 4: Build and Start the Server

```bash
npm start
```

## Deployment Options

### Option 1: Heroku

1. Create a new Heroku app
2. Connect your GitHub repository
3. Set the environment variables in the Heroku dashboard
4. Deploy the app

### Option 2: Vercel

1. Install the Vercel CLI: `npm install -g vercel`
2. Run `vercel` in the project directory
3. Follow the prompts to deploy

### Option 3: Hugging Face Spaces

1. Update the `README.md` file with the appropriate configuration
2. Push your code to a Hugging Face Space repository
3. Configure the environment variables in the Space settings

## Post-Deployment

After deploying, you should:

1. Test the authentication system
2. Verify that the Socket.io connection is working
3. Test the game functionality
4. Monitor the server logs for any errors

## Troubleshooting

- If you encounter CORS issues, make sure your Socket.io server is properly configured
- If authentication fails, check your Supabase configuration
- If the database queries fail, verify your database schema and RLS policies

## Scaling Considerations

As your user base grows, consider:

1. Implementing a caching layer (Redis)
2. Setting up a load balancer
3. Optimizing database queries
4. Implementing rate limiting

## Maintenance

Regularly:

1. Update dependencies
2. Monitor server performance
3. Back up your database
4. Check for security vulnerabilities
