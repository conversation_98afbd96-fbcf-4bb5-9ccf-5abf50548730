Build me a browser-based RPG where you collect and train unique creatures while exploring a vibrant world. Capture creatures with different abilities, form a team of companions, and battle wild creatures you encounter. Level up through combat, gather resources with various professions, and trade items in the player marketplace. Whether you're exploring new areas, developing your creature collection, or mastering professions like mining and fishing. Make the game ready to be deployed as soon as you're finished. Include all things necessary for this game to be multiplayer and go live immediately.
I want to build a text based game that's identical to simple-mmo.com. But I want the ability to capture enemies, anywhere from random animals to undead zombies or skeletons or even dragons. Basically a text based dungeons and dragons but in this game you can capture your enemies and then collect them similar to pokemon but with the elements of dragon warrior 3 for the gameboy color. I want the combat system to be the same. I want there to also be professions and a home building aspect similar to the way runescape does it. For the professions create an energy system that grows as your characters level grows. Make the combat system also apply for the player themselves. Allow the players to step like in simple-mmo for experience. For the professions and the steps, allow players to add a companion to get experience and depending on the action theyre doing the partner may also get bonus experience or give the player a bonus to whatever theyre doing. For example if im fishing and my partner is some sort of aquatic npc then I would get a small chance of getting additional fish. Whenever the player does professions it has to be done on an energy system that replenishes on a 2 minute timer. 1 energy per 2 minutes. But the amount of energy the player can have increases per level. The companions and the players do not have level caps. And also the player can also fight the enemies themselves if their companions are all knocked out. Create a chat system where peopel can talk and also create a market system like simple-mmo where players can trade or sell stuff on. Make certain things be only accessible in certain maps, I.E. if you are on map 1 which is beginner map then you can only mine copper and tin ore and next map is iron. And then also put a level cap on the professions at 999. As you progress in levels it becomes exponentially harder to level to the next. Allow for equipments for each profession that are also level locked like runescapes profession system. Create a capture system for capturing enemies but do not use balls. This game must be copyright infringement free. I want it to be all original. Use something like a digital card or make your own idea on that. Create a collection area like the PC system in pokemon but again it all has to be original and not copy righted.
CreatureQuest is a browser-based RPG focused on creature collection, exploration, and progression. Players capture, train, and battle with unique creatures while developing professions and engaging with a player-driven economy.  Gameplay Systems Character Progression Player Leveling: Players gain experience through exploration, battles, and profession activities Stats: Health increases with level; gold is earned through activities Energy System: 20 base energy for profession activities, scaling with level Experience Requirements: Increasing XP needed for each level Creature Collection Capture Mechanics: Encounter wild creatures during exploration with chance to capture Creature Types: Various types (fire, water, earth, etc.) with strengths/weaknesses Creature Stats: Health, level, experience, abilities Team Building: Up to 6 creatures in active team, with one primary companion Creature Leveling: Creatures gain experience alongside the player Exploration System Areas: Multiple regions with level requirements and unique creature spawns Step-Based Exploration: Each step has random chances for: Finding gold Discovering items Encountering wild creatures Gaining experience Area Progression: New areas unlock as player level increases No Auto-Step: Manual exploration for more engaging gameplay Battle System Turn-Based Combat: Strategic battles using creature abilities Type Advantages: Creature types have effectiveness relationships Abilities: Each creature has unique combat abilities Team Switching: Option to switch active creatures during battle Rewards: Experience, gold, and capture opportunities Profession System Types: Mining, Herbalism, Fishing, and Crafting Resource Gathering: Each profession yields different resources Energy Consumption: Activities require energy that replenishes over time Profession Leveling: Gain experience and levels in each profession Tiered Resources: Different quality resources based on area and profession level Mining: Copper/Tin/Iron and higher tiers Similar progression for other professions Companion Bonuses: Creatures provide small bonuses (max 5%, 0.01% per level) to gathering Companion System Active Companion: One primary creature accompanies player Passive Benefits: Companions help with resource gathering Type-Based Bonuses: Different creature types boost different professions Offline Progression: Companions continue gathering while player is away Optional Selection: Players can explore without companions if desired Economy & Trading Marketplace: Player-driven economy for buying/selling items and resources Listing System: Create listings with quantity and price Resource Value: Resources have intrinsic value but don't directly convert to gold Item Categories: Equipment, resources, and creatures can be traded Technical Requirements User Interface Responsive Design: Fully optimized for both desktop and mobile Main Sections: Exploration, Team, Inventory, Market, Professions, Battle Real-Time Updates: Dynamic content updates without page reloads Streamlined Navigation: Bottom navigation for mobile, sidebar for desktop Data Persistence Database Integration: Supabase PostgreSQL database User Authentication: Email/password login system Data Structure: Normalized database design for users, creatures, inventory, etc. Session Management: Persistent login across sessions Visual Design Art Style: Clean, accessible interface with distinctive creature designs Environmental Theming: Visual differentiation between areas Animation: Subtle animations for battles and important actions Color Coding: Type-based color system for quick recognition Development Priorities Core exploration and creature collection mechanics Battle system with type advantages Profession and resource gathering systems Marketplace and economy Mobile optimization Offline progression Future Expansion Possibilities Events: Limited-time special creatures and resources Guilds: Cooperative play with shared benefits Creature Breeding: Combine creatures for new variants Quests: Structured objectives with special rewards Seasonal Content: Rotating availability of certain creatures and resources. Include a signup page and chat system that is fully functional and working for a massive multiplayer system.