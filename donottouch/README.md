---
title: CreatureQuest
emoji: 🐉
colorFrom: purple
colorTo: blue
sdk: docker
pinned: true
tags:
  - game
  - rpg
  - multiplayer
---

# CreatureQuest

A browser-based RPG where you collect and train unique creatures while exploring a vibrant world.

## Features

- **Creature Collection**: Capture and train unique creatures with different abilities
- **Exploration**: Discover new areas and encounter wild creatures
- **Battle System**: Strategic turn-based battles with type advantages
- **Professions**: Gather resources through mining, fishing, herbalism, and blacksmithing
- **Marketplace**: Trade items and creatures with other players
- **Multiplayer**: Real-time chat and player interactions

## Tech Stack

- Frontend: HTML, CSS (TailwindCSS), JavaScript
- Backend: Node.js, Express, Socket.io
- Database: Supabase (PostgreSQL)
- Authentication: Supabase Auth

## Deployment

See [DEPLOYMENT.md](DEPLOYMENT.md) for detailed deployment instructions.

## Development

1. Clone the repository
2. Install dependencies: `npm install`
3. Set up environment variables in `.env`
4. Start the development server: `npm run dev`

## License

MIT

Check out the configuration reference at https://huggingface.co/docs/hub/spaces-config-reference