const express = require('express');
const http = require('http');
const { Server } = require('socket.io');
const path = require('path');
const fs = require('fs');
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Initialize Express app
const app = express();
const server = http.createServer(app);

// Initialize Socket.io
const io = new Server(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// Middleware to inject Supabase credentials
app.use((req, res, next) => {
  if (req.path === '/index.html' || req.path === '/') {
    fs.readFile(path.join(__dirname, 'index.html'), 'utf8', (err, data) => {
      if (err) {
        return next(err);
      }

      // Inject Supabase credentials
      const injectedData = data.replace('</head>', `
        <script>
          window.SUPABASE_URL = "${process.env.SUPABASE_URL}";
          window.SUPABASE_KEY = "${process.env.SUPABASE_KEY}";
        </script>
        </head>
      `);

      res.send(injectedData);
    });
  } else {
    next();
  }
});

// Serve static files
app.use(express.static(path.join(__dirname, '/')));

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok', timestamp: new Date().toISOString() });
});

// Serve index.html for all other routes
app.get('*', (req, res, next) => {
  if (req.path !== '/index.html' && req.path !== '/' && req.path !== '/health') {
    res.sendFile(path.join(__dirname, 'index.html'));
  }
});

// Track online players
const onlinePlayers = new Set();

// Socket.io connection handling
io.on('connection', (socket) => {
  console.log('A user connected');

  // Handle player login
  socket.on('playerLogin', (playerData) => {
    console.log(`Player logged in: ${playerData.name}`);
    socket.playerData = playerData;
    onlinePlayers.add(playerData.name);

    // Broadcast player connected
    socket.broadcast.emit('playerConnected', playerData.name);

    // Send online players list to all clients
    io.emit('onlinePlayers', Array.from(onlinePlayers));
  });

  // Handle chat messages
  socket.on('chatMessage', (message) => {
    if (socket.playerData) {
      io.emit('chatMessage', {
        player: socket.playerData.name,
        message: message
      });
    }
  });

  // Handle player disconnect
  socket.on('disconnect', () => {
    console.log('A user disconnected');
    if (socket.playerData) {
      onlinePlayers.delete(socket.playerData.name);
      io.emit('playerDisconnected', socket.playerData.name);
      io.emit('onlinePlayers', Array.from(onlinePlayers));
    }
  });
});

// Start server
const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});
