<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CreatureQuest - Browser RPG</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* Type colors */
        .type-normal { background-color: #A8A878; }
        .type-fire { background-color: #F08030; }
        .type-water { background-color: #6890F0; }
        .type-earth { background-color: #E0C068; }
        .type-wind { background-color: #A890F0; }
        .type-plant { background-color: #78C850; }
        .type-ice { background-color: #98D8D8; }
        .type-metal { background-color: #B8B8D0; }
        .type-shadow { background-color: #705848; }
        .type-light { background-color: #F8D030; }
        .type-arcane { background-color: #F85888; }
        .type-toxic { background-color: #A040A0; }

        /* Animations */
        .progress-bar { transition: width 0.5s ease; }
        .chat-message { animation: fadeIn 0.3s ease-in; }
        .capture-animation { animation: pulse 0.5s infinite alternate; }
        .battle-animation { animation: shake 0.3s ease infinite; }
        .fade-in { animation: fadeIn 0.5s ease-in; }

        @keyframes fadeIn { from { opacity: 0; } to { opacity: 1; } }
        @keyframes pulse { from { transform: scale(1); } to { transform: scale(1.05); } }
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        /* Mobile bottom nav */
        @media (max-width: 768px) {
            .mobile-bottom-nav {
                position: fixed;
                bottom: 0;
                left: 0;
                right: 0;
                z-index: 1000;
                background-color: #1F2937;
                box-shadow: 0 -2px 5px rgba(0,0,0,0.2);
            }
        }
    </style>
</head>
<body class="bg-gray-900 text-gray-100 min-h-screen">
    <!-- Auth Screen -->
    <div id="auth-screen" class="fixed inset-0 flex items-center justify-center bg-gray-900 z-50">
        <div class="bg-gray-800 rounded-lg p-8 max-w-md w-full mx-4 fade-in">
            <div class="text-center mb-8">
                <i class="fas fa-dragon text-6xl text-purple-500 mb-4"></i>
                <h1 class="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-blue-500">CreatureQuest</h1>
                <p class="text-gray-400 mt-2">Collect. Train. Conquer.</p>
            </div>

            <div id="auth-forms">
                <!-- Login Form -->
                <div id="login-form">
                    <h2 class="text-xl font-bold mb-4">Login</h2>
                    <div class="mb-4">
                        <label class="block text-gray-400 mb-2">Email</label>
                        <input type="email" id="login-email" class="w-full bg-gray-700 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500">
                    </div>
                    <div class="mb-6">
                        <label class="block text-gray-400 mb-2">Password</label>
                        <input type="password" id="login-password" class="w-full bg-gray-700 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500">
                    </div>
                    <button id="login-btn" class="w-full bg-purple-600 hover:bg-purple-700 rounded-lg px-4 py-3 font-bold mb-4">
                        Login
                    </button>
                    <div class="text-center text-sm">
                        <span class="text-gray-400">Don't have an account? </span>
                        <button id="show-signup" class="text-purple-400 hover:text-purple-300">Sign up</button>
                    </div>
                    <div id="login-error" class="text-red-400 text-sm mt-2 hidden"></div>
                </div>

                <!-- Signup Form -->
                <div id="signup-form" class="hidden">
                    <h2 class="text-xl font-bold mb-4">Create Account</h2>
                    <div class="mb-4">
                        <label class="block text-gray-400 mb-2">Username</label>
                        <input type="text" id="signup-username" class="w-full bg-gray-700 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500">
                    </div>
                    <div class="mb-4">
                        <label class="block text-gray-400 mb-2">Email</label>
                        <input type="email" id="signup-email" class="w-full bg-gray-700 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500">
                    </div>
                    <div class="mb-6">
                        <label class="block text-gray-400 mb-2">Password</label>
                        <input type="password" id="signup-password" class="w-full bg-gray-700 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500">
                    </div>
                    <button id="signup-btn" class="w-full bg-purple-600 hover:bg-purple-700 rounded-lg px-4 py-3 font-bold mb-4">
                        Create Account
                    </button>
                    <div class="text-center text-sm">
                        <span class="text-gray-400">Already have an account? </span>
                        <button id="show-login" class="text-purple-400 hover:text-purple-300">Login</button>
                    </div>
                    <div id="signup-error" class="text-red-400 text-sm mt-2 hidden"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Game Container -->
    <div id="game-container" class="hidden container mx-auto px-4 pb-20 md:pb-6">
        <!-- Header -->
        <header class="flex justify-between items-center mb-6 border-b border-gray-700 py-4">
            <div class="flex items-center">
                <img id="player-avatar" src="https://api.dicebear.com/7.x/adventurer/svg?seed=Player" class="w-10 h-10 rounded-full mr-3">
                <div>
                    <h2 class="font-bold" id="player-name">Adventurer</h2>
                    <div class="text-xs text-gray-400">Lv. <span id="player-level">1</span></div>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <div class="bg-gray-800 rounded-lg px-3 py-1 flex items-center">
                    <i class="fas fa-coins text-yellow-400 mr-1"></i>
                    <span id="gold">1,000</span>
                </div>
                <div class="bg-gray-800 rounded-lg px-3 py-1 flex items-center">
                    <i class="fas fa-bolt text-blue-400 mr-1"></i>
                    <span id="energy">20/20</span>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <!-- Left Sidebar (Desktop) -->
            <div class="hidden lg:block lg:col-span-1 space-y-4">
                <!-- Navigation -->
                <div class="bg-gray-800 rounded-lg p-4">
                    <div class="space-y-2">
                        <button data-screen="explore" class="w-full text-left px-3 py-2 rounded bg-gray-700 hover:bg-gray-600 flex items-center nav-btn">
                            <i class="fas fa-map-marked-alt mr-2 text-blue-400"></i> Explore
                        </button>
                        <button data-screen="team" class="w-full text-left px-3 py-2 rounded hover:bg-gray-700 flex items-center nav-btn">
                            <i class="fas fa-users mr-2 text-purple-400"></i> Team
                        </button>
                        <button data-screen="battle" class="w-full text-left px-3 py-2 rounded hover:bg-gray-700 flex items-center nav-btn">
                            <i class="fas fa-swords mr-2 text-red-400"></i> Battle
                        </button>
                        <button data-screen="professions" class="w-full text-left px-3 py-2 rounded hover:bg-gray-700 flex items-center nav-btn">
                            <i class="fas fa-tools mr-2 text-yellow-400"></i> Professions
                        </button>
                        <button data-screen="market" class="w-full text-left px-3 py-2 rounded hover:bg-gray-700 flex items-center nav-btn">
                            <i class="fas fa-store mr-2 text-orange-400"></i> Marketplace
                        </button>
                        <button data-screen="inventory" class="w-full text-left px-3 py-2 rounded hover:bg-gray-700 flex items-center nav-btn">
                            <i class="fas fa-backpack mr-2 text-green-400"></i> Inventory
                        </button>
                    </div>
                </div>

                <!-- Active Companion -->
                <div class="bg-gray-800 rounded-lg p-4">
                    <h3 class="font-bold mb-3">Active Companion</h3>
                    <div id="active-companion" class="bg-gray-700 rounded p-3 text-center">
                        <div class="text-gray-400 py-4">
                            <i class="fas fa-paw text-3xl mb-2"></i>
                            <p>No companion active</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Game Area -->
            <div class="lg:col-span-2 space-y-4">
                <!-- Game Screens -->
                <div id="game-screen" class="bg-gray-800 rounded-lg min-h-[500px]">
                    <!-- Explore Screen -->
                    <div id="explore-screen" class="p-4">
                        <div class="flex justify-between items-center mb-4">
                            <h2 class="text-xl font-bold">Explore</h2>
                            <div class="flex items-center space-x-2">
                                <span class="text-sm bg-gray-700 px-3 py-1 rounded">Map: <span id="current-map">Starter Plains</span></span>
                                <button id="travel-btn" class="bg-blue-600 hover:bg-blue-700 px-3 py-1 rounded text-sm">
                                    <i class="fas fa-location-arrow mr-1"></i> Travel
                                </button>
                            </div>
                        </div>

                        <div class="bg-gray-700 rounded-lg p-4 mb-4">
                            <div class="flex justify-between items-center mb-3">
                                <h3 class="font-bold">Actions</h3>
                                <div class="text-sm">
                                    Energy: <span id="explore-energy">20</span>/<span id="max-energy">20</span>
                                </div>
                            </div>

                            <div class="grid grid-cols-2 gap-3">
                                <button id="step-btn" class="bg-green-600 hover:bg-green-700 px-3 py-3 rounded-lg font-bold flex items-center justify-center">
                                    <i class="fas fa-shoe-prints mr-2"></i> Take Step
                                </button>
                                <button id="search-btn" class="bg-purple-600 hover:bg-purple-700 px-3 py-3 rounded-lg font-bold flex items-center justify-center">
                                    <i class="fas fa-search mr-2"></i> Search Area
                                </button>
                            </div>
                        </div>

                        <div id="explore-log" class="bg-gray-900 rounded-lg p-3 h-64 overflow-y-auto font-mono text-sm space-y-2">
                            <div class="text-gray-400 italic">Welcome to CreatureQuest! Take your first step to begin your adventure.</div>
                        </div>
                    </div>

                    <!-- Team Screen -->
                    <div id="team-screen" class="hidden p-4">
                        <div class="flex justify-between items-center mb-4">
                            <h2 class="text-xl font-bold">Your Team</h2>
                            <div class="flex space-x-2">
                                <button class="bg-gray-700 hover:bg-gray-600 px-3 py-1 rounded text-sm">All</button>
                                <button class="bg-gray-700 hover:bg-gray-600 px-3 py-1 rounded text-sm">By Type</button>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-3 mb-4" id="team-grid">
                            <!-- Team slots will be added here -->
                            <div class="bg-gray-700 rounded-lg p-4 text-center">
                                <div class="text-gray-400 py-10">
                                    <i class="fas fa-users text-3xl mb-2"></i>
                                    <p>Your team is empty</p>
                                </div>
                            </div>
                        </div>

                        <h3 class="font-bold mb-3">Collection</h3>
                        <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3" id="collection-grid">
                            <!-- Collection will be added here -->
                            <div class="bg-gray-700 rounded-lg p-3 text-center">
                                <div class="text-gray-400 py-6">
                                    <i class="fas fa-book-open text-2xl mb-2"></i>
                                    <p class="text-sm">No creatures collected yet</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Battle Screen -->
                    <div id="battle-screen" class="hidden p-4">
                        <div class="flex justify-between items-center mb-4">
                            <h2 class="text-xl font-bold">Battle</h2>
                            <div class="flex space-x-2">
                                <button id="wild-battle-btn" class="bg-red-600 hover:bg-red-700 px-3 py-1 rounded text-sm">
                                    <i class="fas fa-paw mr-1"></i> Wild
                                </button>
                                <button id="pvp-battle-btn" class="bg-purple-600 hover:bg-purple-700 px-3 py-1 rounded text-sm">
                                    <i class="fas fa-user-ninja mr-1"></i> PvP
                                </button>
                            </div>
                        </div>

                        <div id="battle-arena" class="bg-gray-700 rounded-lg p-6 text-center">
                            <div class="max-w-md mx-auto py-10">
                                <i class="fas fa-trophy text-4xl text-yellow-400 mb-4"></i>
                                <h3 class="text-xl font-bold mb-2">Battle Arena</h3>
                                <p class="text-gray-400 mb-4">Challenge wild creatures or duel other players</p>
                                <div class="flex justify-center space-x-4">
                                    <button id="quick-battle-btn" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg font-bold">
                                        <i class="fas fa-bolt mr-2"></i> Quick Battle
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Professions Screen -->
                    <div id="professions-screen" class="hidden p-4">
                        <div class="flex justify-between items-center mb-4">
                            <h2 class="text-xl font-bold">Professions</h2>
                            <div class="flex space-x-2">
                                <button class="bg-gray-700 hover:bg-gray-600 px-3 py-1 rounded text-sm">Leaderboard</button>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                            <!-- Mining -->
                            <div class="profession-card bg-gray-700 rounded-lg p-4">
                                <div class="flex justify-between items-start mb-3">
                                    <div>
                                        <h3 class="font-bold">Mining</h3>
                                        <p class="text-sm text-gray-400">Lv. <span class="profession-level" data-profession="mining">1</span></p>
                                    </div>
                                    <i class="fas fa-mountain text-3xl text-yellow-500"></i>
                                </div>
                                <div class="w-full bg-gray-600 rounded-full h-2 mb-3">
                                    <div class="bg-yellow-500 h-2 rounded-full profession-progress" style="width: 0%"></div>
                                </div>
                                <button class="w-full bg-yellow-600 hover:bg-yellow-700 px-4 py-2 rounded font-bold mine-btn">
                                    <i class="fas fa-hammer mr-2"></i> Mine (5 Energy)
                                </button>
                            </div>

                            <!-- Fishing -->
                            <div class="profession-card bg-gray-700 rounded-lg p-4">
                                <div class="flex justify-between items-start mb-3">
                                    <div>
                                        <h3 class="font-bold">Fishing</h3>
                                        <p class="text-sm text-gray-400">Lv. <span class="profession-level" data-profession="fishing">1</span></p>
                                    </div>
                                    <i class="fas fa-fish text-3xl text-blue-400"></i>
                                </div>
                                <div class="w-full bg-gray-600 rounded-full h-2 mb-3">
                                    <div class="bg-blue-500 h-2 rounded-full profession-progress" style="width: 0%"></div>
                                </div>
                                <button class="w-full bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded font-bold fish-btn">
                                    <i class="fas fa-fishing-rod mr-2"></i> Fish (5 Energy)
                                </button>
                            </div>

                            <!-- Herbalism -->
                            <div class="profession-card bg-gray-700 rounded-lg p-4">
                                <div class="flex justify-between items-start mb-3">
                                    <div>
                                        <h3 class="font-bold">Herbalism</h3>
                                        <p class="text-sm text-gray-400">Lv. <span class="profession-level" data-profession="herbalism">1</span></p>
                                    </div>
                                    <i class="fas fa-leaf text-3xl text-green-400"></i>
                                </div>
                                <div class="w-full bg-gray-600 rounded-full h-2 mb-3">
                                    <div class="bg-green-500 h-2 rounded-full profession-progress" style="width: 0%"></div>
                                </div>
                                <button class="w-full bg-green-600 hover:bg-green-700 px-4 py-2 rounded font-bold herb-btn">
                                    <i class="fas fa-seedling mr-2"></i> Gather (5 Energy)
                                </button>
                            </div>

                            <!-- Blacksmithing -->
                            <div class="profession-card bg-gray-700 rounded-lg p-4">
                                <div class="flex justify-between items-start mb-3">
                                    <div>
                                        <h3 class="font-bold">Blacksmithing</h3>
                                        <p class="text-sm text-gray-400">Lv. <span class="profession-level" data-profession="blacksmithing">1</span></p>
                                    </div>
                                    <i class="fas fa-hammer text-3xl text-orange-500"></i>
                                </div>
                                <div class="w-full bg-gray-600 rounded-full h-2 mb-3">
                                    <div class="bg-orange-500 h-2 rounded-full profession-progress" style="width: 0%"></div>
                                </div>
                                <button class="w-full bg-orange-600 hover:bg-orange-700 px-4 py-2 rounded font-bold smith-btn">
                                    <i class="fas fa-fire mr-2"></i> Smith (10 Energy)
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Marketplace Screen -->
                    <div id="marketplace-screen" class="hidden p-4">
                        <div class="flex justify-between items-center mb-4">
                            <h2 class="text-xl font-bold">Marketplace</h2>
                            <div class="flex space-x-2">
                                <button class="bg-gray-700 hover:bg-gray-600 px-3 py-1 rounded text-sm">Buy</button>
                                <button class="bg-gray-700 hover:bg-gray-600 px-3 py-1 rounded text-sm">Sell</button>
                            </div>
                        </div>

                        <div class="bg-gray-700 rounded-lg p-4 mb-4">
                            <div class="flex justify-between items-center mb-3">
                                <h3 class="font-bold">Featured Items</h3>
                                <div class="flex space-x-2">
                                    <button class="bg-gray-600 hover:bg-gray-500 px-3 py-1 rounded text-sm">Resources</button>
                                    <button class="bg-gray-600 hover:bg-gray-500 px-3 py-1 rounded text-sm">Equipment</button>
                                    <button class="bg-gray-600 hover:bg-gray-500 px-3 py-1 rounded text-sm">Creatures</button>
                                </div>
                            </div>

                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                                <div class="bg-gray-800 rounded-lg p-3">
                                    <div class="flex justify-between items-start mb-2">
                                        <div>
                                            <h4 class="font-bold">Copper Ore</h4>
                                            <p class="text-xs text-gray-400">Mining resource</p>
                                        </div>
                                        <div class="bg-yellow-600 rounded-full h-8 w-8 flex items-center justify-center">
                                            <i class="fas fa-gem"></i>
                                        </div>
                                    </div>
                                    <div class="flex justify-between items-center mt-3">
                                        <div class="text-yellow-400 font-bold">25 <i class="fas fa-coins ml-1"></i></div>
                                        <button class="bg-blue-600 hover:bg-blue-700 px-3 py-1 rounded text-sm">Buy</button>
                                    </div>
                                </div>

                                <div class="bg-gray-800 rounded-lg p-3">
                                    <div class="flex justify-between items-start mb-2">
                                        <div>
                                            <h4 class="font-bold">Basic Rod</h4>
                                            <p class="text-xs text-gray-400">Fishing equipment</p>
                                        </div>
                                        <div class="bg-blue-600 rounded-full h-8 w-8 flex items-center justify-center">
                                            <i class="fas fa-fishing-rod"></i>
                                        </div>
                                    </div>
                                    <div class="flex justify-between items-center mt-3">
                                        <div class="text-yellow-400 font-bold">150 <i class="fas fa-coins ml-1"></i></div>
                                        <button class="bg-blue-600 hover:bg-blue-700 px-3 py-1 rounded text-sm">Buy</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <h3 class="font-bold mb-3">Recent Transactions</h3>
                        <div class="bg-gray-700 rounded-lg p-3">
                            <div class="text-center text-gray-400 py-4">
                                <i class="fas fa-exchange-alt text-2xl mb-2"></i>
                                <p>No recent transactions</p>
                            </div>
                        </div>
                    </div>

                    <!-- Inventory Screen -->
                    <div id="inventory-screen" class="hidden p-4">
                        <div class="flex justify-between items-center mb-4">
                            <h2 class="text-xl font-bold">Inventory</h2>
                            <div class="flex space-x-2">
                                <button class="bg-gray-700 hover:bg-gray-600 px-3 py-1 rounded text-sm">Resources</button>
                                <button class="bg-gray-700 hover:bg-gray-600 px-3 py-1 rounded text-sm">Equipment</button>
                            </div>
                        </div>

                        <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3" id="inventory-grid">
                            <div class="bg-gray-700 rounded-lg p-3 text-center">
                                <div class="text-gray-400 py-6">
                                    <i class="fas fa-backpack text-2xl mb-2"></i>
                                    <p class="text-sm">Inventory empty</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Sidebar - Chat -->
            <div class="lg:col-span-1 space-y-4">
                <!-- Global Chat -->
                <div class="bg-gray-800 rounded-lg p-4 h-[400px] flex flex-col">
                    <div class="flex justify-between items-center mb-3">
                        <h3 class="font-bold">Global Chat</h3>
                        <div class="flex space-x-1">
                            <button class="bg-gray-700 hover:bg-gray-600 p-1 rounded text-xs">Global</button>
                            <button class="bg-gray-700 hover:bg-gray-600 p-1 rounded text-xs">Trade</button>
                        </div>
                    </div>

                    <div id="chat-messages" class="flex-1 overflow-y-auto mb-3 space-y-2 text-sm">
                        <div class="chat-message">
                            <span class="font-bold text-blue-400">System:</span> Welcome to CreatureQuest!
                        </div>
                    </div>

                    <div class="flex">
                        <input type="text" id="chat-input" class="flex-1 bg-gray-700 rounded-l-lg px-3 py-2 text-sm focus:outline-none" placeholder="Type your message...">
                        <button id="chat-send" class="bg-blue-600 hover:bg-blue-700 px-3 py-2 rounded-r-lg">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>

                <!-- Online Players -->
                <div class="bg-gray-800 rounded-lg p-4">
                    <h3 class="font-bold mb-3">Online Players</h3>
                    <div id="online-players" class="space-y-2 text-sm">
                        <div class="flex items-center">
                            <div class="w-2 h-2 rounded-full bg-green-500 mr-2"></div>
                            <span>Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile Bottom Navigation -->
    <div class="mobile-bottom-nav lg:hidden">
        <div class="flex justify-around items-center h-16">
            <button data-screen="explore" class="flex flex-col items-center justify-center w-full h-full nav-btn">
                <i class="fas fa-map-marked-alt text-blue-400 mb-1"></i>
                <span class="text-xs">Explore</span>
            </button>
            <button data-screen="team" class="flex flex-col items-center justify-center w-full h-full nav-btn">
                <i class="fas fa-users text-purple-400 mb-1"></i>
                <span class="text-xs">Team</span>
            </button>
            <button data-screen="battle" class="flex flex-col items-center justify-center w-full h-full nav-btn">
                <i class="fas fa-swords text-red-400 mb-1"></i>
                <span class="text-xs">Battle</span>
            </button>
            <button data-screen="professions" class="flex flex-col items-center justify-center w-full h-full nav-btn">
                <i class="fas fa-tools text-yellow-400 mb-1"></i>
                <span class="text-xs">Professions</span>
            </button>
        </div>
    </div>

    <!-- Encounter Modal -->
    <div id="encounter-modal" class="hidden fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50">
        <div class="bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
            <div id="encounter-content" class="text-center">
                <div id="wild-creature" class="mb-6">
                    <div class="type-normal rounded-full w-24 h-24 mx-auto mb-3 flex items-center justify-center text-4xl">
                        <i class="fas fa-paw"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-1">Wild Creature</h3>
                    <div class="text-sm text-gray-400 mb-4">Level 1 Normal-type</div>

                    <div class="bg-gray-700 rounded-lg p-3 mb-4">
                        <div class="flex justify-between text-xs mb-1">
                            <span>HP</span>
                            <span>10/10</span>
                        </div>
                        <div class="w-full bg-gray-600 rounded-full h-2">
                            <div class="bg-green-500 h-2 rounded-full" style="width: 100%"></div>
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-2 gap-3">
                    <button id="battle-btn" class="bg-red-600 hover:bg-red-700 px-4 py-2 rounded font-bold">
                        <i class="fas fa-swords mr-1"></i> Battle
                    </button>
                    <button id="capture-btn" class="bg-purple-600 hover:bg-purple-700 px-4 py-2 rounded font-bold">
                        <i class="fas fa-capture mr-1"></i> Capture
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Capture Modal -->
    <div id="capture-modal" class="hidden fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50">
        <div class="bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
            <div class="text-center">
                <div class="type-normal rounded-full w-24 h-24 mx-auto mb-3 flex items-center justify-center text-4xl capture-animation">
                    <i class="fas fa-paw"></i>
                </div>
                <h3 class="text-xl font-bold mb-4">Attempting to capture...</h3>

                <div class="bg-gray-700 rounded-lg p-4 mb-4">
                    <div class="flex justify-between items-center mb-2">
                        <span>Capture Chance:</span>
                        <span class="font-bold">45%</span>
                    </div>
                    <div class="w-full bg-gray-600 rounded-full h-2">
                        <div class="bg-purple-500 h-2 rounded-full" style="width: 45%"></div>
                    </div>
                </div>

                <div id="capture-result" class="hidden">
                    <div class="text-green-400 font-bold text-lg mb-4">
                        <i class="fas fa-check-circle mr-1"></i> Capture Successful!
                    </div>
                    <button id="capture-close" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded font-bold">
                        Continue
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Battle Modal -->
    <div id="battle-modal" class="hidden fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50">
        <div class="bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
            <div class="grid grid-cols-1 gap-6">
                <!-- Enemy Creature -->
                <div class="bg-gray-700 rounded-lg p-4">
                    <div class="flex justify-between items-start mb-2">
                        <div>
                            <h3 class="font-bold text-lg">Wild Creature</h3>
                            <div class="text-sm text-gray-400">Level 1 Normal-type</div>
                        </div>
                        <div class="type-normal px-3 py-1 rounded-full text-xs font-bold">NORMAL</div>
                    </div>

                    <div class="bg-gray-600 rounded-lg p-3 mb-3">
                        <div class="flex justify-between text-xs mb-1">
                            <span>HP</span>
                            <span id="enemy-hp">10/10</span>
                        </div>
                        <div class="w-full bg-gray-500 rounded-full h-2">
                            <div id="enemy-hp-bar" class="bg-red-500 h-2 rounded-full" style="width: 100%"></div>
                        </div>
                    </div>

                    <div class="text-center py-4">
                        <div class="type-normal rounded-full w-32 h-32 mx-auto flex items-center justify-center text-6xl battle-animation">
                            <i class="fas fa-paw"></i>
                        </div>
                    </div>
                </div>

                <!-- Player Creature -->
                <div class="bg-gray-700 rounded-lg p-4">
                    <div class="flex justify-between items-start mb-2">
                        <div>
                            <h3 class="font-bold text-lg">Your Companion</h3>
                            <div class="text-sm text-gray-400">Level 1 Normal-type</div>
                        </div>
                        <div class="type-normal px-3 py-1 rounded-full text-xs font-bold">NORMAL</div>
                    </div>

                    <div class="bg-gray-600 rounded-lg p-3 mb-3">
                        <div class="flex justify-between text-xs mb-1">
                            <span>HP</span>
                            <span id="player-hp">10/10</span>
                        </div>
                        <div class="w-full bg-gray-500 rounded-full h-2">
                            <div id="player-hp-bar" class="bg-green-500 h-2 rounded-full" style="width: 100%"></div>
                        </div>
                    </div>

                    <div class="text-center py-4">
                        <div class="type-normal rounded-full w-32 h-32 mx-auto flex items-center justify-center text-6xl">
                            <i class="fas fa-paw"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Battle Log -->
            <div class="bg-gray-900 rounded-lg p-3 mt-4 h-32 overflow-y-auto text-sm">
                <div id="battle-log" class="space-y-1">
                    <div>A wild creature appeared!</div>
                </div>
            </div>

            <!-- Battle Actions -->
            <div class="grid grid-cols-2 gap-3 mt-4">
                <button id="attack-btn" class="bg-red-600 hover:bg-red-700 px-4 py-2 rounded font-bold">
                    <i class="fas fa-sword mr-1"></i> Attack
                </button>
                <button id="special-btn" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded font-bold">
                    <i class="fas fa-bolt mr-1"></i> Special
                </button>
                <button id="item-btn" class="bg-purple-600 hover:bg-purple-700 px-4 py-2 rounded font-bold">
                    <i class="fas fa-bag mr-1"></i> Item
                </button>
                <button id="flee-btn" class="bg-gray-600 hover:bg-gray-500 px-4 py-2 rounded font-bold">
                    <i class="fas fa-running mr-1"></i> Flee
                </button>
            </div>
        </div>
    </div>

    <script>
        // Supabase Configuration - Will be populated from environment variables on the server
        const supabaseUrl = window.SUPABASE_URL || 'https://your-supabase-url.supabase.co';
        const supabaseKey = window.SUPABASE_KEY || 'your-supabase-key';
        const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);

        // Socket.io connection
        const socket = io(window.location.origin);

        // Game State
        const gameState = {
            player: {
                id: null,
                name: "Adventurer",
                email: "",
                level: 1,
                exp: 0,
                maxExp: 100,
                gold: 1000,
                energy: 20,
                maxEnergy: 20,
                map: "Starter Plains",
                team: [],
                collection: [],
                activeCompanion: null,
                inventory: [],
                professions: {
                    mining: { level: 1, exp: 0, maxExp: 100 },
                    fishing: { level: 1, exp: 0, maxExp: 100 },
                    herbalism: { level: 1, exp: 0, maxExp: 100 },
                    blacksmithing: { level: 1, exp: 0, maxExp: 100 }
                }
            },
            maps: [
                { name: "Starter Plains", minLevel: 1, maxLevel: 5, resources: ["copper", "tin", "herbs"] },
                { name: "Forest of Whispers", minLevel: 5, maxLevel: 10, resources: ["iron", "fish", "rare herbs"] },
                { name: "Mountain Peaks", minLevel: 10, maxLevel: 15, resources: ["silver", "gemstones", "mountain herbs"] }
            ],
            creatureTypes: ["normal", "fire", "water", "earth", "wind", "plant", "ice", "metal", "shadow", "light", "arcane", "toxic"],
            creatureIcons: {
                normal: "fa-paw",
                fire: "fa-fire",
                water: "fa-tint",
                earth: "fa-mountain",
                wind: "fa-wind",
                plant: "fa-leaf",
                ice: "fa-icicles",
                metal: "fa-shield-alt",
                shadow: "fa-moon",
                light: "fa-sun",
                arcane: "fa-hat-wizard",
                toxic: "fa-skull-crossbones"
            },
            session: null
        };

        // DOM Elements
        const authScreen = document.getElementById('auth-screen');
        const gameContainer = document.getElementById('game-container');
        const loginForm = document.getElementById('login-form');
        const signupForm = document.getElementById('signup-form');
        const showSignup = document.getElementById('show-signup');
        const showLogin = document.getElementById('show-login');
        const loginBtn = document.getElementById('login-btn');
        const signupBtn = document.getElementById('signup-btn');
        const loginError = document.getElementById('login-error');
        const signupError = document.getElementById('signup-error');

        // Initialize the game
        function init() {
            // Check for existing session
            const session = supabase.auth.session();
            if (session) {
                gameState.session = session;
                loadPlayerData();
                authScreen.classList.add('hidden');
                gameContainer.classList.remove('hidden');
            } else {
                authScreen.classList.remove('hidden');
                gameContainer.classList.add('hidden');
            }

            // Set up event listeners
            setupEventListeners();
        }

        // Set up all event listeners
        function setupEventListeners() {
            // Auth forms
            showSignup.addEventListener('click', () => {
                loginForm.classList.add('hidden');
                signupForm.classList.remove('hidden');
            });

            showLogin.addEventListener('click', () => {
                signupForm.classList.add('hidden');
                loginForm.classList.remove('hidden');
            });

            // Login
            loginBtn.addEventListener('click', async () => {
                const email = document.getElementById('login-email').value;
                const password = document.getElementById('login-password').value;

                if (!email || !password) {
                    loginError.textContent = "Please enter both email and password";
                    loginError.classList.remove('hidden');
                    return;
                }

                const { user, error } = await supabase.auth.signIn({
                    email,
                    password
                });

                if (error) {
                    loginError.textContent = error.message;
                    loginError.classList.remove('hidden');
                } else {
                    gameState.session = supabase.auth.session();
                    loadPlayerData();
                    authScreen.classList.add('hidden');
                    gameContainer.classList.remove('hidden');
                }
            });

            // Signup
            signupBtn.addEventListener('click', async () => {
                const username = document.getElementById('signup-username').value;
                const email = document.getElementById('signup-email').value;
                const password = document.getElementById('signup-password').value;

                if (!username || !email || !password) {
                    signupError.textContent = "Please fill in all fields";
                    signupError.classList.remove('hidden');
                    return;
                }

                if (password.length < 6) {
                    signupError.textContent = "Password must be at least 6 characters";
                    signupError.classList.remove('hidden');
                    return;
                }

                // Create user
                const { user, error } = await supabase.auth.signUp({
                    email,
                    password
                });

                if (error) {
                    signupError.textContent = error.message;
                    signupError.classList.remove('hidden');
                } else {
                    // Create player profile
                    const { data, error: profileError } = await supabase
                        .from('players')
                        .insert([
                            {
                                id: user.id,
                                username,
                                email,
                                level: 1,
                                gold: 1000,
                                energy: 20,
                                max_energy: 20
                            }
                        ]);

                    if (profileError) {
                        signupError.textContent = "Error creating profile: " + profileError.message;
                        signupError.classList.remove('hidden');
                    } else {
                        // Show login form
                        signupForm.classList.add('hidden');
                        loginForm.classList.remove('hidden');
                        document.getElementById('login-email').value = email;
                        document.getElementById('login-password').value = password;

                        // Show success message
                        loginError.textContent = "Account created! Please login.";
                        loginError.classList.remove('hidden');
                        loginError.classList.remove('text-red-400');
                        loginError.classList.add('text-green-400');
                    }
                }
            });

            // Navigation buttons
            document.querySelectorAll('.nav-btn').forEach(btn => {
                btn.addEventListener('click', () => {
                    const screen = btn.dataset.screen;
                    showScreen(screen);
                });
            });

            // Chat system
            document.getElementById('chat-send').addEventListener('click', sendChatMessage);
            document.getElementById('chat-input').addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    sendChatMessage();
                }
            });

            // Game actions
            document.getElementById('step-btn').addEventListener('click', takeStep);
            document.getElementById('search-btn').addEventListener('click', searchArea);
            document.getElementById('travel-btn').addEventListener('click', travel);
            document.getElementById('quick-battle-btn').addEventListener('click', startQuickBattle);

            // Profession actions
            document.querySelector('.mine-btn').addEventListener('click', () => performProfessionAction('mining'));
            document.querySelector('.fish-btn').addEventListener('click', () => performProfessionAction('fishing'));
            document.querySelector('.herb-btn').addEventListener('click', () => performProfessionAction('herbalism'));
            document.querySelector('.smith-btn').addEventListener('click', () => performProfessionAction('blacksmithing'));

            // Socket.io events
            socket.on('chatMessage', (data) => {
                const messageDiv = document.createElement('div');
                messageDiv.className = 'chat-message';
                messageDiv.innerHTML = `<span class="font-bold text-blue-400">${data.player}:</span> ${data.message}`;
                document.getElementById('chat-messages').appendChild(messageDiv);
                document.getElementById('chat-messages').scrollTop = document.getElementById('chat-messages').scrollHeight;
            });

            socket.on('playerConnected', (player) => {
                addNotification(`${player} connected`, 'fa-user-plus', 'text-green-400');
            });

            socket.on('playerDisconnected', (player) => {
                addNotification(`${player} disconnected`, 'fa-user-times', 'text-red-400');
            });

            socket.on('onlinePlayers', (players) => {
                const onlinePlayersDiv = document.getElementById('online-players');
                onlinePlayersDiv.innerHTML = '';

                players.forEach(player => {
                    const playerDiv = document.createElement('div');
                    playerDiv.className = 'flex items-center';
                    playerDiv.innerHTML = `
                        <div class="w-2 h-2 rounded-full bg-green-500 mr-2"></div>
                        <span>${player}</span>
                    `;
                    onlinePlayersDiv.appendChild(playerDiv);
                });
            });
        }

        // Load player data from Supabase
        async function loadPlayerData() {
            if (!gameState.session) return;

            // Load player profile
            const { data: player, error } = await supabase
                .from('players')
                .select('*')
                .eq('id', gameState.session.user.id)
                .single();

            if (player) {
                gameState.player = {
                    ...gameState.player,
                    id: player.id,
                    name: player.username,
                    email: player.email,
                    level: player.level,
                    exp: player.exp || 0,
                    maxExp: player.max_exp || 100,
                    gold: player.gold,
                    energy: player.energy,
                    maxEnergy: player.max_energy,
                    map: player.map || "Starter Plains"
                };

                // Load player's team
                const { data: team } = await supabase
                    .from('creatures')
                    .select('*')
                    .eq('player_id', gameState.session.user.id)
                    .eq('in_team', true);

                if (team) {
                    gameState.player.team = team;

                    // Set active companion if any
                    const active = team.find(c => c.is_active);
                    if (active) {
                        gameState.player.activeCompanion = active;
                    }
                }

                // Load player's collection
                const { data: collection } = await supabase
                    .from('creatures')
                    .select('*')
                    .eq('player_id', gameState.session.user.id);

                if (collection) {
                    gameState.player.collection = collection;
                }

                // Load player's professions
                const { data: professions } = await supabase
                    .from('professions')
                    .select('*')
                    .eq('player_id', gameState.session.user.id);

                if (professions) {
                    professions.forEach(prof => {
                        if (gameState.player.professions[prof.name]) {
                            gameState.player.professions[prof.name] = {
                                level: prof.level,
                                exp: prof.exp,
                                maxExp: prof.max_exp
                            };
                        }
                    });
                }

                // Update UI with loaded data
                updateUI();

                // Connect to chat
                socket.emit('playerConnected', gameState.player.name);
            }
        }

        // Save player data to Supabase
        async function savePlayerData() {
            if (!gameState.session) return;

            // Update player profile
            await supabase
                .from('players')
                .update({
                    level: gameState.player.level,
                    exp: gameState.player.exp,
                    max_exp: gameState.player.maxExp,
                    gold: gameState.player.gold,
                    energy: gameState.player.energy,
                    max_energy: gameState.player.maxEnergy,
                    map: gameState.player.map,
                    updated_at: new Date()
                })
                .eq('id', gameState.session.user.id);

            // Update professions
            for (const [name, prof] of Object.entries(gameState.player.professions)) {
                await supabase
                    .from('professions')
                    .upsert({
                        player_id: gameState.session.user.id,
                        name,
                        level: prof.level,
                        exp: prof.exp,
                        max_exp: prof.maxExp
                    }, { onConflict: ['player_id', 'name'] });
            }
        }

        // Show a specific game screen
        function showScreen(screen) {
            document.querySelectorAll('#game-screen > div').forEach(div => {
                div.classList.add('hidden');
            });

            document.getElementById(`${screen}-screen`).classList.remove('hidden');

            // Update active nav button
            document.querySelectorAll('.nav-btn').forEach(btn => {
                if (btn.dataset.screen === screen) {
                    btn.classList.add('bg-gray-700');
                } else {
                    btn.classList.remove('bg-gray-700');
                }
            });
        }

        // Update UI with current game state
        function updateUI() {
            document.getElementById('player-name').textContent = gameState.player.name;
            document.getElementById('player-level').textContent = gameState.player.level;
            document.getElementById('gold').textContent = gameState.player.gold.toLocaleString();
            document.getElementById('energy').textContent = `${gameState.player.energy}/${gameState.player.maxEnergy}`;
            document.getElementById('explore-energy').textContent = gameState.player.energy;
            document.getElementById('max-energy').textContent = gameState.player.maxEnergy;
            document.getElementById('current-map').textContent = gameState.player.map;

            // Update active companion display
            updateActiveCompanion();

            // Update professions display
            updateProfessionsDisplay();
        }

        // Update active companion display
        function updateActiveCompanion() {
            const activeCompanionDiv = document.getElementById('active-companion');

            if (!gameState.player.activeCompanion) {
                activeCompanionDiv.innerHTML = `
                    <div class="text-gray-400 py-4">
                        <i class="fas fa-paw text-3xl mb-2"></i>
                        <p>No companion active</p>
                    </div>
                `;
                return;
            }

            const creature = gameState.player.activeCompanion;
            const typeClass = `type-${creature.type.toLowerCase()}`;
            const icon = gameState.creatureIcons[creature.type.toLowerCase()] || 'fa-paw';

            activeCompanionDiv.innerHTML = `
                <div class="flex justify-between items-start mb-2">
                    <div>
                        <h3 class="font-bold">${creature.name}</h3>
                        <div class="text-xs text-gray-400">Lv. ${creature.level} ${creature.type}-type</div>
                    </div>
                    <div class="${typeClass} px-2 py-1 rounded-full text-xs font-bold">${creature.type.toUpperCase()}</div>
                </div>
                <div class="text-center py-2">
                    <div class="${typeClass} rounded-full w-16 h-16 mx-auto flex items-center justify-center text-3xl">
                        <i class="fas ${icon}"></i>
                    </div>
                </div>
                <div class="bg-gray-600 rounded-lg p-2 mt-2">
                    <div class="flex justify-between text-xs mb-1">
                        <span>HP</span>
                        <span>${creature.hp}/${creature.max_hp}</span>
                    </div>
                    <div class="w-full bg-gray-500 rounded-full h-1">
                        <div class="bg-green-500 h-1 rounded-full" style="width: ${(creature.hp / creature.max_hp) * 100}%"></div>
                    </div>
                </div>
            `;
        }

        // Update professions display
        function updateProfessionsDisplay() {
            document.querySelectorAll('.profession-level').forEach(el => {
                const profession = el.dataset.profession;
                el.textContent = gameState.player.professions[profession].level;
            });

            document.querySelectorAll('.profession-progress').forEach(el => {
                const profession = el.dataset.profession;
                const progress = gameState.player.professions[profession];
                const width = (progress.exp / progress.maxExp) * 100;
                el.style.width = `${width}%`;
            });
        }

        // Take a step in exploration
        function takeStep() {
            if (gameState.player.energy < 1) {
                addToLog("You don't have enough energy!");
                return;
            }

            gameState.player.energy -= 1;
            const expGain = Math.floor(Math.random() * 5) + 3;
            gameState.player.exp += expGain;

            addToLog(`You took a step and gained ${expGain} experience!`);

            // Check for level up
            checkLevelUp();

            // Random encounter chance (20%)
            if (Math.random() < 0.2) {
                setTimeout(() => {
                    triggerRandomEncounter();
                }, 500);
            }

            updateUI();
            savePlayerData();
        }

        // Search the area
        function searchArea() {
            if (gameState.player.energy < 2) {
                addToLog("You don't have enough energy!");
                return;
            }

            gameState.player.energy -= 2;

            // Higher chance to find something (50%)
            if (Math.random() < 0.5) {
                const foundItems = [
                    { name: "Herbs", icon: "fa-leaf", color: "text-green-400" },
                    { name: "Copper Ore", icon: "fa-gem", color: "text-yellow-400" },
                    { name: "Fish", icon: "fa-fish", color: "text-blue-400" }
                ];

                const item = foundItems[Math.floor(Math.random() * foundItems.length)];
                addToLog(`You found some ${item.name}! <i class="fas ${item.icon} ${item.color}"></i>`);

                // Add to inventory
                gameState.player.inventory.push({
                    name: item.name,
                    type: 'resource',
                    quantity: 1
                });

                // Higher encounter chance (40%)
                if (Math.random() < 0.4) {
                    setTimeout(() => {
                        triggerRandomEncounter();
                    }, 500);
                }
            } else {
                addToLog("You searched the area but didn't find anything interesting.");
            }

            updateUI();
            savePlayerData();
        }

        // Travel to a new map
        function travel() {
            const currentMapIndex = gameState.maps.findIndex(m => m.name === gameState.player.map);
            let nextMapIndex = currentMapIndex + 1;

            if (nextMapIndex >= gameState.maps.length) {
                nextMapIndex = 0;
            }

            const nextMap = gameState.maps[nextMapIndex];

            // Check if player is high enough level
            if (gameState.player.level < nextMap.minLevel) {
                addToLog(`You need to be at least level ${nextMap.minLevel} to travel to ${nextMap.name}.`);
                return;
            }

            gameState.player.map = nextMap.name;
            addToLog(`You traveled to ${nextMap.name}!`);
            updateUI();
            savePlayerData();
        }

        // Perform a profession action
        async function performProfessionAction(profession) {
            if (gameState.player.energy < 5) {
                addToLog("You don't have enough energy!");
                return;
            }

            const energyCost = profession === 'blacksmithing' ? 10 : 5;

            gameState.player.energy -= energyCost;
            const expGain = Math.floor(Math.random() * 10) + 5;
            gameState.player.professions[profession].exp += expGain;

            // Check for profession level up
            if (gameState.player.professions[profession].exp >= gameState.player.professions[profession].maxExp) {
                gameState.player.professions[profession].level += 1;
                gameState.player.professions[profession].exp -= gameState.player.professions[profession].maxExp;
                gameState.player.professions[profession].maxExp = Math.floor(gameState.player.professions[profession].maxExp * 1.2);

                addToLog(`Your ${profession} skill increased to level ${gameState.player.professions[profession].level}!`);
            }

            // Get resource based on profession and current map
            let resource = '';
            switch(profession) {
                case 'mining':
                    resource = gameState.player.map.includes('Forest') ? 'Iron Ore' :
                              gameState.player.map.includes('Mountain') ? 'Silver Ore' : 'Copper Ore';
                    break;
                case 'fishing':
                    resource = 'Fish';
                    break;
                case 'herbalism':
                    resource = gameState.player.map.includes('Forest') ? 'Rare Herbs' :
                              gameState.player.map.includes('Mountain') ? 'Mountain Herbs' : 'Common Herbs';
                    break;
                case 'blacksmithing':
                    resource = 'Metal Bar';
                    break;
            }

            // Add resource to inventory
            const existingItem = gameState.player.inventory.find(item => item.name === resource);
            if (existingItem) {
                existingItem.quantity += 1;
            } else {
                gameState.player.inventory.push({
                    name: resource,
                    type: 'resource',
                    quantity: 1
                });
            }

            addToLog(`You used ${profession} and gained ${expGain} experience! Found: ${resource}`);

            updateUI();
            updateProfessionsDisplay();
            savePlayerData();
        }

        // Trigger a random encounter
        function triggerRandomEncounter() {
            const currentMap = gameState.maps.find(m => m.name === gameState.player.map);
            const creatureLevel = Math.floor(Math.random() * (currentMap.maxLevel - currentMap.minLevel + 1)) + currentMap.minLevel;
            const creatureType = gameState.creatureTypes[Math.floor(Math.random() * gameState.creatureTypes.length)];
            const creatureIcon = gameState.creatureIcons[creatureType] || 'fa-paw';

            const creature = {
                name: getRandomCreatureName(creatureType),
                type: creatureType,
                level: creatureLevel,
                hp: creatureLevel * 5,
                max_hp: creatureLevel * 5
            };

            // Update encounter modal
            const encounterContent = document.getElementById('wild-creature');
            encounterContent.querySelector('.type-normal').className = `type-${creatureType} rounded-full w-24 h-24 mx-auto mb-3 flex items-center justify-center text-4xl`;
            encounterContent.querySelector('i').className = `fas ${creatureIcon}`;
            encounterContent.querySelector('h3').textContent = `Wild ${creature.name}`;
            encounterContent.querySelector('.text-sm').textContent = `Level ${creature.level} ${creatureType}-type`;

            // Show encounter modal
            document.getElementById('encounter-modal').classList.remove('hidden');

            // Set up battle button
            document.getElementById('battle-btn').onclick = () => {
                document.getElementById('encounter-modal').classList.add('hidden');
                startBattle(creature);
            };

            // Set up capture button
            document.getElementById('capture-btn').onclick = () => {
                document.getElementById('encounter-modal').classList.add('hidden');
                attemptCapture(creature);
            };
        }

        // Get random creature name based on type
        function getRandomCreatureName(type) {
            const prefixes = {
                normal: ["Common", "Plain", "Simple", "Regular"],
                fire: ["Blazing", "Flaming", "Scorching", "Inferno"],
                water: ["Aqua", "Tidal", "Marine", "Ocean"],
                earth: ["Rocky", "Earthen", "Mountain", "Stone"],
                wind: ["Gusty", "Breezy", "Zephyr", "Aerial"],
                plant: ["Leafy", "Vine", "Floral", "Forest"],
                ice: ["Frost", "Glacial", "Icy", "Chill"],
                metal: ["Iron", "Steel", "Metallic", "Bronze"],
                shadow: ["Dark", "Shadow", "Night", "Umbra"],
                light: ["Radiant", "Luminous", "Bright", "Shining"],
                arcane: ["Mystic", "Enchanted", "Arcane", "Magical"],
                toxic: ["Venomous", "Toxic", "Poison", "Noxious"]
            };

            const suffixes = ["Beast", "Creature", "Entity", "Being", "Sprite", "Spirit"];

            const typePrefixes = prefixes[type] || prefixes.normal;
            const prefix = typePrefixes[Math.floor(Math.random() * typePrefixes.length)];
            const suffix = suffixes[Math.floor(Math.random() * suffixes.length)];

            return `${prefix} ${suffix}`;
        }

        // Attempt to capture a creature
        async function attemptCapture(creature) {
            const captureContent = document.getElementById('capture-modal');
            captureContent.querySelector('.type-normal').className = `type-${creature.type} rounded-full w-24 h-24 mx-auto mb-3 flex items-center justify-center text-4xl capture-animation`;
            captureContent.querySelector('i').className = `fas ${gameState.creatureIcons[creature.type] || 'fa-paw'}`;

            // Calculate capture chance (base 30% + level difference)
            let captureChance = 30;
            captureChance -= (creature.level - gameState.player.level) * 5;
            captureChance = Math.max(10, Math.min(90, captureChance));

            document.querySelector('#capture-modal .font-bold').textContent = `${captureChance}%`;
            document.querySelector('#capture-modal .bg-purple-500').style.width = `${captureChance}%`;

            document.getElementById('capture-modal').classList.remove('hidden');

            // Hide capture result if shown
            document.getElementById('capture-result').classList.add('hidden');

            // Simulate capture attempt after delay
            setTimeout(async () => {
                const success = Math.random() * 100 < captureChance;

                if (success) {
                    // Add to collection
                    const newCreature = {
                        ...creature,
                        player_id: gameState.session.user.id,
                        in_team: false,
                        is_active: false,
                        captured_at: new Date()
                    };

                    // Save to database
                    const { data, error } = await supabase
                        .from('creatures')
                        .insert([newCreature]);

                    if (!error) {
                        gameState.player.collection.push(newCreature);

                        document.getElementById('capture-result').innerHTML = `
                            <div class="text-green-400 font-bold text-lg mb-4">
                                <i class="fas fa-check-circle mr-1"></i> Capture Successful!
                            </div>
                            <button id="capture-close" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded font-bold">
                                Continue
                            </button>
                        `;

                        addToLog(`You successfully captured the ${creature.name}!`);
                    } else {
                        document.getElementById('capture-result').innerHTML = `
                            <div class="text-red-400 font-bold text-lg mb-4">
                                <i class="fas fa-times-circle mr-1"></i> Capture Failed!
                            </div>
                            <button id="capture-close" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded font-bold">
                                Continue
                            </button>
                        `;

                        addToLog(`Failed to capture the ${creature.name}!`);
                    }
                } else {
                    document.getElementById('capture-result').innerHTML = `
                        <div class="text-red-400 font-bold text-lg mb-4">
                            <i class="fas fa-times-circle mr-1"></i> Capture Failed!
                        </div>
                        <button id="capture-close" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded font-bold">
                            Continue
                        </button>
                    `;

                    addToLog(`The ${creature.name} broke free!`);
                }

                document.getElementById('capture-result').classList.remove('hidden');

                document.getElementById('capture-close').addEventListener('click', () => {
                    document.getElementById('capture-modal').classList.add('hidden');
                });
            }, 2000);
        }

        // Start a quick battle
        function startQuickBattle() {
            const currentMap = gameState.maps.find(m => m.name === gameState.player.map);
            const creatureLevel = Math.floor(Math.random() * (currentMap.maxLevel - currentMap.minLevel + 1)) + currentMap.minLevel;
            const creatureType = gameState.creatureTypes[Math.floor(Math.random() * gameState.creatureTypes.length)];

            const creature = {
                name: getRandomCreatureName(creatureType),
                type: creatureType,
                level: creatureLevel,
                hp: creatureLevel * 5,
                max_hp: creatureLevel * 5
            };

            startBattle(creature);
        }

        // Start a battle
        function startBattle(enemy) {
            const playerCreature = gameState.player.activeCompanion || {
                name: "You",
                type: "normal",
                level: gameState.player.level,
                hp: gameState.player.level * 10,
                max_hp: gameState.player.level * 10
            };

            // Update battle modal
            document.querySelector('#battle-modal .bg-gray-700:first-child h3').textContent = `Wild ${enemy.name}`;
            document.querySelector('#battle-modal .bg-gray-700:first-child .text-sm').textContent = `Level ${enemy.level} ${enemy.type}-type`;
            document.querySelector('#battle-modal .bg-gray-700:first-child .type-normal').className = `type-${enemy.type} rounded-full w-32 h-32 mx-auto flex items-center justify-center text-6xl battle-animation`;
            document.querySelector('#battle-modal .bg-gray-700:first-child i').className = `fas ${gameState.creatureIcons[enemy.type] || 'fa-paw'}`;
            document.querySelector('#battle-modal .bg-gray-700:first-child .bg-red-500').style.width = '100%';
            document.getElementById('enemy-hp').textContent = `${enemy.hp}/${enemy.max_hp}`;
            document.getElementById('enemy-hp-bar').style.width = '100%';

            document.querySelector('#battle-modal .bg-gray-700:last-child h3').textContent = playerCreature.name;
            document.querySelector('#battle-modal .bg-gray-700:last-child .text-sm').textContent = `Level ${playerCreature.level} ${playerCreature.type}-type`;
            document.querySelector('#battle-modal .bg-gray-700:last-child .type-normal').className = `type-${playerCreature.type} rounded-full w-32 h-32 mx-auto flex items-center justify-center text-6xl`;
            document.querySelector('#battle-modal .bg-gray-700:last-child i').className = `fas ${gameState.creatureIcons[playerCreature.type] || 'fa-paw'}`;
            document.querySelector('#battle-modal .bg-gray-700:last-child .bg-green-500').style.width = '100%';
            document.getElementById('player-hp').textContent = `${playerCreature.hp}/${playerCreature.max_hp}`;
            document.getElementById('player-hp-bar').style.width = '100%';

            document.getElementById('battle-log').innerHTML = `<div>A wild ${enemy.name} appeared!</div>`;

            document.getElementById('battle-modal').classList.remove('hidden');

            // Battle state
            const battleState = {
                enemy: JSON.parse(JSON.stringify(enemy)),
                playerCreature: JSON.parse(JSON.stringify(playerCreature)),
                playerTurn: true
            };

            // Battle actions
            document.getElementById('attack-btn').onclick = () => {
                if (!battleState.playerTurn) return;

                // Player attack
                const damage = Math.floor(Math.random() * 3) + 1 + battleState.playerCreature.level;
                battleState.enemy.hp = Math.max(0, battleState.enemy.hp - damage);

                addToBattleLog(`${battleState.playerCreature.name} attacks for ${damage} damage!`);
                updateBattleUI(battleState);

                // Check if enemy is defeated
                if (battleState.enemy.hp <= 0) {
                    endBattle(true, battleState);
                    return;
                }

                battleState.playerTurn = false;

                // Enemy attack after delay
                setTimeout(() => {
                    enemyAttack(battleState);
                }, 1000);
            };

            document.getElementById('special-btn').onclick = () => {
                if (!battleState.playerTurn) return;

                // Player special attack (costs energy)
                if (gameState.player.energy < 5) {
                    addToBattleLog("Not enough energy for a special attack!");
                    return;
                }

                gameState.player.energy -= 5;
                const damage = Math.floor(Math.random() * 5) + 3 + battleState.playerCreature.level;
                battleState.enemy.hp = Math.max(0, battleState.enemy.hp - damage);

                addToBattleLog(`${battleState.playerCreature.name} uses a special attack for ${damage} damage!`);
                updateBattleUI(battleState);
                updateUI();

                // Check if enemy is defeated
                if (battleState.enemy.hp <= 0) {
                    endBattle(true, battleState);
                    return;
                }

                battleState.playerTurn = false;

                // Enemy attack after delay
                setTimeout(() => {
                    enemyAttack(battleState);
                }, 1000);
            };

            document.getElementById('item-btn').onclick = () => {
                addToBattleLog("No items available yet!");
            };

            document.getElementById('flee-btn').onclick = () => {
                addToBattleLog("You fled from battle!");
                document.getElementById('battle-modal').classList.add('hidden');
            };
        }

        // Enemy attack function
        function enemyAttack(battleState) {
            const damage = Math.floor(Math.random() * 3) + 1 + battleState.enemy.level;
            battleState.playerCreature.hp = Math.max(0, battleState.playerCreature.hp - damage);

            addToBattleLog(`${battleState.enemy.name} attacks for ${damage} damage!`);
            updateBattleUI(battleState);

            // Check if player is defeated
            if (battleState.playerCreature.hp <= 0) {
                endBattle(false, battleState);
                return;
            }

            battleState.playerTurn = true;
        }

        // Update battle UI
        function updateBattleUI(battleState) {
            document.getElementById('enemy-hp').textContent = `${battleState.enemy.hp}/${battleState.enemy.max_hp}`;
            document.getElementById('enemy-hp-bar').style.width = `${(battleState.enemy.hp / battleState.enemy.max_hp) * 100}%`;

            document.getElementById('player-hp').textContent = `${battleState.playerCreature.hp}/${battleState.playerCreature.max_hp}`;
            document.getElementById('player-hp-bar').style.width = `${(battleState.playerCreature.hp / battleState.playerCreature.max_hp) * 100}%`;
        }

        // End battle
        function endBattle(playerWon, battleState) {
            if (playerWon) {
                const expGain = battleState.enemy.level * 10;
                const goldGain = battleState.enemy.level * 5;

                gameState.player.exp += expGain;
                gameState.player.gold += goldGain;

                addToBattleLog(`Victory! You gained ${expGain} experience and ${goldGain} gold!`);
                checkLevelUp();
            } else {
                addToBattleLog("You were defeated!");
            }

            setTimeout(() => {
                document.getElementById('battle-modal').classList.add('hidden');
                updateUI();
                savePlayerData();
            }, 2000);
        }

        // Add message to battle log
        function addToBattleLog(message) {
            const battleLog = document.getElementById('battle-log');
            const messageDiv = document.createElement('div');
            messageDiv.textContent = message;
            battleLog.appendChild(messageDiv);
            battleLog.scrollTop = battleLog.scrollHeight;
        }

        // Check for level up
        function checkLevelUp() {
            if (gameState.player.exp >= gameState.player.maxExp) {
                gameState.player.level += 1;
                gameState.player.exp -= gameState.player.maxExp;
                gameState.player.maxExp = Math.floor(gameState.player.maxExp * 1.5);
                gameState.player.maxEnergy += 5;
                gameState.player.energy = gameState.player.maxEnergy; // Full restore on level up

                addToLog(`Level up! You are now level ${gameState.player.level}!`);
                addNotification(`Level up! Now level ${gameState.player.level}`, 'fa-star', 'text-yellow-400');
            }
        }

        // Add message to exploration log
        function addToLog(message) {
            const log = document.getElementById('explore-log');
            const messageDiv = document.createElement('div');
            messageDiv.innerHTML = message;
            messageDiv.className = 'fade-in';
            log.appendChild(messageDiv);
            log.scrollTop = log.scrollHeight;

            // Keep only last 50 messages
            while (log.children.length > 50) {
                log.removeChild(log.firstChild);
            }
        }

        // Send chat message
        function sendChatMessage() {
            const input = document.getElementById('chat-input');
            const message = input.value.trim();

            if (message && gameState.session) {
                socket.emit('chatMessage', message);
                input.value = '';
            }
        }

        // Add notification
        function addNotification(message, icon, colorClass) {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 bg-gray-800 border border-gray-600 rounded-lg p-3 z-50 fade-in ${colorClass}`;
            notification.innerHTML = `
                <div class="flex items-center">
                    <i class="fas ${icon} mr-2"></i>
                    <span>${message}</span>
                </div>
            `;

            document.body.appendChild(notification);

            // Remove after 3 seconds
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // Energy regeneration (1 energy per 2 minutes)
        setInterval(() => {
            if (gameState.player.energy < gameState.player.maxEnergy) {
                gameState.player.energy += 1;
                updateUI();
                savePlayerData();
            }
        }, 120000); // 2 minutes

        // Auto-save every 30 seconds
        setInterval(() => {
            if (gameState.session) {
                savePlayerData();
            }
        }, 30000);

        // Initialize the game when page loads
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>