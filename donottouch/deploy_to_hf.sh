#!/bin/bash

# Check if Hugging Face CLI is installed
if ! command -v huggingface-cli &> /dev/null
then
    echo "Hugging Face CLI not found. Installing..."
    pip install huggingface_hub
fi

# Check if user is logged in
if ! huggingface-cli whoami &> /dev/null
then
    echo "Please login to Hugging Face:"
    huggingface-cli login
fi

# Get space name
read -p "Enter your Hugging Face username: " username
read -p "Enter space name (default: creaturequest): " spacename
spacename=${spacename:-creaturequest}

# Create or update the space
echo "Creating/updating space: $username/$spacename"
huggingface-cli repo create "$spacename" --type space

# Clone the space repository
git clone "https://huggingface.co/spaces/$username/$spacename" hf_space
cd hf_space

# Copy files
cp -r ../index.html ../style.css ../server.js ../package.json ../Dockerfile ../docker-compose.yml ../README.md .

# Update README.md with correct space name
sed -i "s/title: CreatureQuest/title: $spacename/g" README.md

# Commit and push changes
git add .
git commit -m "Update space with CreatureQuest game"
git push

echo "Deployment complete! Your game is now available at: https://huggingface.co/spaces/$username/$spacename"
echo "Don't forget to set the SUPABASE_URL and SUPABASE_KEY environment variables in the space settings."
