-- Create players table
CREATE TABLE players (
    id UUID PRIMARY KEY REFERENCES auth.users(id),
    username TEXT UNIQUE NOT NULL,
    email TEXT UNIQUE NOT NULL,
    level INTEGER NOT NULL DEFAULT 1,
    exp INTEGER NOT NULL DEFAULT 0,
    max_exp INTEGER NOT NULL DEFAULT 100,
    gold INTEGER NOT NULL DEFAULT 1000,
    energy INTEGER NOT NULL DEFAULT 20,
    max_energy INTEGER NOT NULL DEFAULT 20,
    map TEXT NOT NULL DEFAULT 'Starter Plains',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create creatures table
CREATE TABLE creatures (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    type TEXT NOT NULL,
    level INTEGER NOT NULL DEFAULT 1,
    exp INTEGER NOT NULL DEFAULT 0,
    max_exp INTEGER NOT NULL DEFAULT 100,
    hp INTEGER NOT NULL,
    max_hp INTEGER NOT NULL,
    attack INTEGER NOT NULL,
    defense INTEGER NOT NULL,
    speed INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create player_creatures table (for collection)
CREATE TABLE player_creatures (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    player_id UUID NOT NULL REFERENCES players(id) ON DELETE CASCADE,
    creature_id UUID NOT NULL REFERENCES creatures(id) ON DELETE CASCADE,
    nickname TEXT,
    level INTEGER NOT NULL DEFAULT 1,
    exp INTEGER NOT NULL DEFAULT 0,
    max_exp INTEGER NOT NULL DEFAULT 100,
    hp INTEGER NOT NULL,
    max_hp INTEGER NOT NULL,
    attack INTEGER NOT NULL,
    defense INTEGER NOT NULL,
    speed INTEGER NOT NULL,
    in_team BOOLEAN NOT NULL DEFAULT FALSE,
    is_active BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE (player_id, creature_id)
);

-- Create professions table
CREATE TABLE player_professions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    player_id UUID NOT NULL REFERENCES players(id) ON DELETE CASCADE,
    profession TEXT NOT NULL,
    level INTEGER NOT NULL DEFAULT 1,
    exp INTEGER NOT NULL DEFAULT 0,
    max_exp INTEGER NOT NULL DEFAULT 100,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE (player_id, profession)
);

-- Create inventory table
CREATE TABLE inventory (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    player_id UUID NOT NULL REFERENCES players(id) ON DELETE CASCADE,
    item_type TEXT NOT NULL,
    item_id TEXT NOT NULL,
    quantity INTEGER NOT NULL DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE (player_id, item_type, item_id)
);

-- Create marketplace table
CREATE TABLE marketplace (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    seller_id UUID NOT NULL REFERENCES players(id) ON DELETE CASCADE,
    item_type TEXT NOT NULL,
    item_id TEXT NOT NULL,
    quantity INTEGER NOT NULL DEFAULT 1,
    price INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create RLS policies
ALTER TABLE players ENABLE ROW LEVEL SECURITY;
ALTER TABLE player_creatures ENABLE ROW LEVEL SECURITY;
ALTER TABLE player_professions ENABLE ROW LEVEL SECURITY;
ALTER TABLE inventory ENABLE ROW LEVEL SECURITY;
ALTER TABLE marketplace ENABLE ROW LEVEL SECURITY;

-- Players can read their own data
CREATE POLICY "Players can read their own data" ON players
    FOR SELECT USING (auth.uid() = id);

-- Players can update their own data
CREATE POLICY "Players can update their own data" ON players
    FOR UPDATE USING (auth.uid() = id);

-- Players can read their own creatures
CREATE POLICY "Players can read their own creatures" ON player_creatures
    FOR SELECT USING (auth.uid() = player_id);

-- Players can update their own creatures
CREATE POLICY "Players can update their own creatures" ON player_creatures
    FOR UPDATE USING (auth.uid() = player_id);

-- Players can insert their own creatures
CREATE POLICY "Players can insert their own creatures" ON player_creatures
    FOR INSERT WITH CHECK (auth.uid() = player_id);

-- Players can delete their own creatures
CREATE POLICY "Players can delete their own creatures" ON player_creatures
    FOR DELETE USING (auth.uid() = player_id);

-- Similar policies for professions and inventory
CREATE POLICY "Players can read their own professions" ON player_professions
    FOR SELECT USING (auth.uid() = player_id);

CREATE POLICY "Players can update their own professions" ON player_professions
    FOR UPDATE USING (auth.uid() = player_id);

CREATE POLICY "Players can insert their own professions" ON player_professions
    FOR INSERT WITH CHECK (auth.uid() = player_id);

CREATE POLICY "Players can read their own inventory" ON inventory
    FOR SELECT USING (auth.uid() = player_id);

CREATE POLICY "Players can update their own inventory" ON inventory
    FOR UPDATE USING (auth.uid() = player_id);

CREATE POLICY "Players can insert into their own inventory" ON inventory
    FOR INSERT WITH CHECK (auth.uid() = player_id);

-- Marketplace policies
CREATE POLICY "Anyone can read marketplace" ON marketplace
    FOR SELECT USING (true);

CREATE POLICY "Players can insert their own marketplace listings" ON marketplace
    FOR INSERT WITH CHECK (auth.uid() = seller_id);

CREATE POLICY "Players can update their own marketplace listings" ON marketplace
    FOR UPDATE USING (auth.uid() = seller_id);

CREATE POLICY "Players can delete their own marketplace listings" ON marketplace
    FOR DELETE USING (auth.uid() = seller_id);
