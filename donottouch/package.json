{"name": "creaturequest", "version": "1.0.0", "description": "A browser-based RPG where you collect and train unique creatures", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "setup": "node setup_supabase.js"}, "keywords": ["game", "rpg", "browser-game", "creature-collection"], "author": "", "license": "MIT", "dependencies": {"@supabase/supabase-js": "^2.39.3", "dotenv": "^16.3.1", "express": "^4.18.2", "socket.io": "^4.7.2"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=14.0.0"}}